import React from 'react';
import { Container, Typography, Box, Grid, Paper, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import Footer from '../components/Footer';
import RouterIcon from '@mui/icons-material/Router';
import BarChartIcon from '@mui/icons-material/BarChart';
import NatureIcon from '@mui/icons-material/Nature';
import PublicIcon from '@mui/icons-material/Public';
import { useLanguage } from '../contexts/LanguageContext';

const FeatureCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2.5),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  borderRadius: '15px',
  border: '1px solid rgba(27, 76, 53, 0.1)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 15px 30px rgba(27, 76, 53, 0.15)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

const ActionButton = styled(Button)(({ theme }) => ({
  borderRadius: '50px',
  padding: '10px 30px',
  fontWeight: 600,
  textTransform: 'none',
  fontSize: '1rem',
  marginTop: theme.spacing(2),
  backgroundColor: '#1B4C35',
  '&:hover': {
    backgroundColor: '#4CAF50',
  },
}));

const getFeatures = (language: string) => [
  {
    icon: <RouterIcon sx={{ fontSize: 50, color: '#1B4C35', mb: 2 }} />,
    title: language === 'en' ? 'Real-Time Monitoring' : language === 'kn' ? 'ರಿಯಲ್-ಟೈಮ್ ಮಾನಿಟರಿಂಗ್' : 'रीयल-टाइम मॉनिटरिंग',
    description: language === 'en' ? 'Our IoT device tracks soil moisture, temperature, and other critical metrics in real time, empowering farmers with precise data to optimize crop growth.' :
                language === 'kn' ? 'ನಮ್ಮ ಐಒಟಿ ಸಾಧನವು ಮಣ್ಣಿನ ತೇವಾಂಶ, ತಾಪಮಾನ ಮತ್ತು ಇತರ ಮಹತ್ವದ ಮೆಟ್ರಿಕ್‌ಗಳನ್ನು ನೈಜ ಸಮಯದಲ್ಲಿ ಟ್ರ್ಯಾಕ್ ಮಾಡುತ್ತದೆ, ಬೆಳೆ ಬೆಳವಣಿಗೆಯನ್ನು ಅನುಕೂಲಿಸಲು ನಿಖರವಾದ ಡೇಟಾದೊಂದಿಗೆ ರೈತರನ್ನು ಸಬಲೀಕರಣಗೊಳಿಸುತ್ತದೆ.' :
                'हमारा IoT डिवाइस मिट्टी की नमी, तापमान और अन्य महत्वपूर्ण मैट्रिक्स को वास्तविक समय में ट्रैक करता है, जिससे किसानों को फसल की वृद्धि को अनुकूलित करने के लिए सटीक डेटा के साथ सशक्त बनाया जाता है।'
  },
  {
    icon: <BarChartIcon sx={{ fontSize: 50, color: '#1B4C35', mb: 2 }} />,
    title: language === 'en' ? 'Smart Insights' : language === 'kn' ? 'ಸ್ಮಾರ್ಟ್ ಇನ್‌ಸೈಟ್‌ಗಳು' : 'स्मार्ट इनसाइट्स',
    description: language === 'en' ? 'Leverage advanced analytics to understand your land\'s needs better, with actionable insights delivered straight from the field to your device.' :
                language === 'kn' ? 'ನಿಮ್ಮ ಭೂಮಿಯ ಅಗತ್ಯಗಳನ್ನು ಉತ್ತಮವಾಗಿ ಅರ್ಥಮಾಡಿಕೊಳ್ಳಲು ಸುಧಾರಿತ ವಿಶ್ಲೇಷಣೆಯನ್ನು ಬಳಸಿಕೊಳ್ಳಿ, ಕ್ಷೇತ್ರದಿಂದ ನೇರವಾಗಿ ನಿಮ್ಮ ಸಾಧನಕ್ಕೆ ತಲುಪಿಸಲಾದ ಕ್ರಿಯಾತ್ಮಕ ಒಳನೋಟಗಳೊಂದಿಗೆ.' :
                'अपनी भूमि की जरूरतों को बेहतर ढंग से समझने के लिए उन्नत विश्लेषण का लाभ उठाएं, खेत से सीधे आपके डिवाइस पर पहुंचाए गए कार्रवाई योग्य अंतर्दृष्टि के साथ।'
  },
  {
    icon: <NatureIcon sx={{ fontSize: 50, color: '#1B4C35', mb: 2 }} />,
    title: language === 'en' ? 'Sustainable Farming' : language === 'kn' ? 'ಸುಸ್ಥಿರ ಕೃಷಿ' : 'टिकाऊ खेती',
    description: language === 'en' ? 'Reduce water waste and improve resource efficiency with data-driven decisions, promoting eco-friendly practices for a healthier planet.' :
                language === 'kn' ? 'ಡೇಟಾ-ಆಧಾರಿತ ನಿರ್ಧಾರಗಳೊಂದಿಗೆ ನೀರಿನ ವ್ಯರ್ಥವನ್ನು ಕಡಿಮೆ ಮಾಡಿ ಮತ್ತು ಸಂಪನ್ಮೂಲ ದಕ್ಷತೆಯನ್ನು ಸುಧಾರಿಸಿ, ಆರೋಗ್ಯಕರ ಗ್ರಹಕ್ಕಾಗಿ ಪರಿಸರ-ಸ್ನೇಹಿ ಅಭ್ಯಾಸಗಳನ್ನು ಉತ್ತೇಜಿಸಿ.' :
                'डेटा-संचालित निर्णयों के साथ पानी की बर्बादी को कम करें और संसाधन दक्षता में सुधार करें, स्वस्थ ग्रह के लिए पर्यावरण-अनुकूल प्रथाओं को बढ़ावा दें।'
  },
  {
    icon: <PublicIcon sx={{ fontSize: 50, color: '#1B4C35', mb: 2 }} />,
    title: language === 'en' ? 'Local Innovation' : language === 'kn' ? 'ಸ್ಥಳೀಯ ನಾವೀನ್ಯತೆ' : 'स्थानीय नवाचार',
    description: language === 'en' ? 'Designed to support farmers worldwide, our system integrates seamlessly with local agricultural practices, enhancing productivity from the ground up.' :
                language === 'kn' ? 'ವಿಶ್ವಾದ್ಯಂತ ರೈತರನ್ನು ಬೆಂಬಲಿಸಲು ವಿನ್ಯಾಸಗೊಳಿಸಲಾಗಿದೆ, ನಮ್ಮ ವ್ಯವಸ್ಥೆಯು ಸ್ಥಳೀಯ ಕೃಷಿ ಅಭ್ಯಾಸಗಳೊಂದಿಗೆ ನಿರ್ಬಾಧವಾಗಿ ಸಂಯೋಜಿಸುತ್ತದೆ, ಮೂಲದಿಂದ ಉತ್ಪಾದಕತೆಯನ್ನು ಹೆಚ್ಚಿಸುತ್ತದೆ.' :
                'दुनिया भर के किसानों का समर्थन करने के लिए डिज़ाइन किया गया, हमारा सिस्टम स्थानीय कृषि प्रथाओं के साथ निर्बाध रूप से एकीकृत होता है, जमीनी स्तर से उत्पादकता को बढ़ाता है।'
  }
];

const IoTManagement: React.FC = () => {
  const { language } = useLanguage();
  const featuresList = getFeatures(language);

  const handleLoginClick = () => {
    window.open('https://iot.haegl.in/login', '_blank');
  };

  const handleRegisterClick = () => {
    window.open('https://iot.haegl.in/add_customer/', '_blank');
  };

  const handleExploreClick = () => {
    window.open('https://iot.haegl.in/', '_blank');
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          py: { xs: 4, sm: 6, md: 10 },
          bgcolor: '#f5f5f5',
          backgroundImage: 'linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8))',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={{ xs: 3, md: 4 }} alignItems="center">
            <Grid item xs={12} md={6} order={{ xs: 2, md: 1 }}>
              <Typography
                variant="h3"
                component="h1"
                sx={{
                  color: '#1B4C35',
                  fontWeight: 700,
                  mb: 2,
                  fontSize: { xs: '1.8rem', sm: '2.2rem', md: '3rem' },
                  textAlign: { xs: 'center', md: 'left' }
                }}
              >
                {language === 'en' ? 'IoT Management for Smart Agriculture' :
                 language === 'kn' ? 'ಸ್ಮಾರ್ಟ್ ಕೃಷಿಗಾಗಿ ಐಒಟಿ ನಿರ್ವಹಣೆ' :
                 'स्मार्ट कृषि के लिए आईओटी प्रबंधन'}
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: '#555',
                  mb: 4,
                  fontWeight: 400,
                  fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },
                  textAlign: { xs: 'center', md: 'left' },
                  lineHeight: 1.6
                }}
              >
                {language === 'en' ? 'Revolutionize your farming with our cutting-edge IoT solutions that provide real-time data and insights for optimal crop management.' :
                 language === 'kn' ? 'ಅನುಕೂಲಕರ ಬೆಳೆ ನಿರ್ವಹಣೆಗಾಗಿ ನೈಜ-ಸಮಯದ ಡೇಟಾ ಮತ್ತು ಒಳನೋಟಗಳನ್ನು ಒದಗಿಸುವ ನಮ್ಮ ಅತ್ಯಾಧುನಿಕ ಐಒಟಿ ಪರಿಹಾರಗಳೊಂದಿಗೆ ನಿಮ್ಮ ಕೃಷಿಯನ್ನು ಕ್ರಾಂತಿಗೊಳಿಸಿ.' :
                 'हमारे अत्याधुनिक आईओटी समाधानों के साथ अपनी खेती में क्रांति लाएं जो इष्टतम फसल प्रबंधन के लिए रीयल-टाइम डेटा और अंतर्दृष्टि प्रदान करते हैं।'}
              </Typography>
              <Box sx={{
                display: 'flex',
                gap: 2,
                flexWrap: 'wrap',
                justifyContent: { xs: 'center', md: 'flex-start' }
              }}>
                <ActionButton
                  variant="contained"
                  color="primary"
                  onClick={handleLoginClick}
                  sx={{
                    minWidth: { xs: '120px', sm: '140px' },
                    fontSize: { xs: '0.9rem', sm: '1rem' }
                  }}
                >
                  {language === 'en' ? 'Login' : language === 'kn' ? 'ಲಾಗಿನ್' : 'लॉगिन'}
                </ActionButton>
                <ActionButton
                  variant="outlined"
                  sx={{
                    borderColor: '#1B4C35',
                    color: '#1B4C35',
                    minWidth: { xs: '120px', sm: '140px' },
                    fontSize: { xs: '0.9rem', sm: '1rem' },
                    '&:hover': {
                      borderColor: '#4CAF50',
                      backgroundColor: 'rgba(76, 175, 80, 0.1)'
                    }
                  }}
                  onClick={handleRegisterClick}
                >
                  {language === 'en' ? 'Register' : language === 'kn' ? 'ನೋಂದಣಿ' : 'पंजीकरण'}
                </ActionButton>
              </Box>
            </Grid>
            <Grid item xs={12} md={6} order={{ xs: 1, md: 2 }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  p: { xs: 2, md: 0 }
                }}
              >
                <Box
                  component="img"
                  src="/darvi-images/device1.jpg"
                  alt="IoT Device for Smart Agriculture - Real-time monitoring system with sensors"
                  sx={{
                    width: '100%',
                    height: { xs: '280px', sm: '350px', md: 'auto' },
                    maxHeight: { xs: '280px', sm: '350px', md: '450px' },
                    objectFit: 'cover',
                    borderRadius: '20px',
                    boxShadow: '0 20px 40px rgba(27, 76, 53, 0.15)',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'scale(1.02)',
                      boxShadow: '0 25px 50px rgba(27, 76, 53, 0.25)'
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: { xs: 6, md: 8 } }}>
        <Container maxWidth="lg">
          <Typography
            variant="h4"
            component="h2"
            align="center"
            sx={{
              mb: { xs: 4, md: 6 },
              color: '#1B4C35',
              fontWeight: 600,
              fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' },
              px: { xs: 2, md: 0 }
            }}
          >
            {language === 'en' ? 'Smart Agriculture Features' :
             language === 'kn' ? 'ಸ್ಮಾರ್ಟ್ ಕೃಷಿ ವೈಶಿಷ್ಟ್ಯಗಳು' :
             'स्मार्ट कृषि विशेषताएं'}
          </Typography>
          <Grid container spacing={{ xs: 3, md: 4 }}>
            {featuresList.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <FeatureCard
                  elevation={2}
                  sx={{
                    height: '100%',
                    minHeight: { xs: '200px', sm: '220px' },
                    mx: { xs: 1, sm: 0 }
                  }}
                >
                  {feature.icon}
                  <Typography
                    variant="h6"
                    component="h3"
                    gutterBottom
                    sx={{
                      fontWeight: 600,
                      color: '#1B4C35',
                      fontSize: { xs: '1.1rem', sm: '1.25rem' },
                      textAlign: 'center'
                    }}
                  >
                    {feature.title}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      fontSize: { xs: '0.85rem', sm: '0.875rem' },
                      lineHeight: 1.5,
                      textAlign: 'center'
                    }}
                  >
                    {feature.description}
                  </Typography>
                </FeatureCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box sx={{ py: { xs: 6, md: 8 }, bgcolor: '#1B4C35', color: 'white' }}>
        <Container maxWidth="md">
          <Box sx={{ textAlign: 'center', px: { xs: 2, md: 0 } }}>
            <Typography
              variant="h4"
              component="h2"
              gutterBottom
              sx={{
                fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' },
                fontWeight: 600,
                mb: { xs: 2, md: 3 }
              }}
            >
              {language === 'en' ? 'Ready to Transform Your Farming?' :
               language === 'kn' ? 'ನಿಮ್ಮ ಕೃಷಿಯನ್ನು ಪರಿವರ್ತಿಸಲು ಸಿದ್ಧವಾಗಿದ್ದೀರಾ?' :
               'अपनी खेती को बदलने के लिए तैयार हैं?'}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                mb: { xs: 3, md: 4 },
                opacity: 0.9,
                fontSize: { xs: '1rem', sm: '1.1rem' },
                lineHeight: 1.6,
                maxWidth: '600px',
                mx: 'auto'
              }}
            >
              {language === 'en' ? 'Explore our IoT platform and discover how data-driven agriculture can increase your yield and sustainability.' :
               language === 'kn' ? 'ನಮ್ಮ ಐಒಟಿ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ಅನ್ನು ಅನ್ವೇಷಿಸಿ ಮತ್ತು ಡೇಟಾ-ಆಧಾರಿತ ಕೃಷಿಯು ನಿಮ್ಮ ಇಳುವರಿ ಮತ್ತು ಸುಸ್ಥಿರತೆಯನ್ನು ಹೇಗೆ ಹೆಚ್ಚಿಸಬಹುದು ಎಂಬುದನ್ನು ಕಂಡುಹಿಡಿಯಿರಿ.' :
               'हमारे आईओटी प्लेटफॉर्म का अन्वेषण करें और जानें कि डेटा-संचालित कृषि आपकी उपज और स्थिरता को कैसे बढ़ा सकती है।'}
            </Typography>
            <ActionButton
              variant="contained"
              color="primary"
              sx={{
                bgcolor: '#4CAF50',
                color: 'white',
                fontSize: { xs: '1rem', sm: '1.1rem' },
                px: { xs: 3, sm: 4 },
                py: { xs: 1.5, sm: 2 },
                '&:hover': {
                  bgcolor: '#45a049',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 20px rgba(76, 175, 80, 0.3)'
                }
              }}
              onClick={handleExploreClick}
            >
              {language === 'en' ? 'Explore IoT Platform' :
               language === 'kn' ? 'ಐಒಟಿ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ಅನ್ವೇಷಿಸಿ' :
               'आईओटी प्लेटफॉर्म का अन्वेषण करें'}
            </ActionButton>
          </Box>
        </Container>
      </Box>

      <Footer />
    </Box>
  );
};

export default IoTManagement;
