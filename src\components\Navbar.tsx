import React, { useState } from 'react';
import {
  App<PERSON><PERSON>,
  Toolbar,
  Box,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  useTheme,
  useMediaQuery,
  Container,
  Button,
  Menu,
  MenuItem,
  Divider,
  ToggleButtonGroup,
  ToggleButton
} from '@mui/material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import MenuIcon from '@mui/icons-material/Menu';
import HomeIcon from '@mui/icons-material/Home';
import InfoIcon from '@mui/icons-material/Info';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import QuestionAnswerIcon from '@mui/icons-material/QuestionAnswer';
import CloseIcon from '@mui/icons-material/Close';
import GavelIcon from '@mui/icons-material/Gavel';
import RouterIcon from '@mui/icons-material/Router';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import LanguageIcon from '@mui/icons-material/Language';
import AppRegistrationIcon from '@mui/icons-material/AppRegistration';
import BusinessIcon from '@mui/icons-material/Business';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import LocalFloristIcon from '@mui/icons-material/LocalFlorist';
import { useLanguage } from '../contexts/LanguageContext';
import translations from '../translations';
import LogoFlip from './LogoFlip';

// Import header content from CMS
import headerContent from '../content/layout/header.json';

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [languageAnchorEl, setLanguageAnchorEl] = useState<null | HTMLElement>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { language, setLanguage } = useLanguage();
  const t = translations[language as keyof typeof translations].common;

  // Helper function to safely get language text
  const getLanguageText = (): string => {
    const langKey = language as keyof typeof headerContent.languageSelector;
    if (langKey === 'languages') {
      return 'Language'; // Default fallback
    }

    const text = headerContent.languageSelector[langKey];
    if (typeof text === 'string') {
      return text;
    }

    return headerContent.languageSelector.en as string;
  };

  // Check if current page is the registration form
  const isRegistrationPage = location.pathname === '/form';

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handlePoliciesClick = () => {
    navigate('/terms');
    if (isMobile) {
      handleDrawerToggle();
    }
  };

  const handleLanguageClick = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setLanguageAnchorEl(null);
  };

  // Handle nursery PDF download
  const handleNurseryClick = () => {
    const pdfUrl = '/Darvi plants price list 2024.pdf';

    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = 'Darvi plants price list 2024.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Also open the PDF in a new tab for viewing
    window.open(pdfUrl, '_blank');
  };

  const handleLanguageChange = (newLanguage: 'en' | 'kn' | 'hi') => {
    setLanguage(newLanguage);
    handleLanguageClose();
  };

  // Get menu items from CMS data
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'Home': return <HomeIcon />;
      case 'Info': return <InfoIcon />;
      case 'Business': return <BusinessIcon />;
      case 'AttachMoney': return <AttachMoneyIcon />;
      case 'Router': return <RouterIcon />;
      case 'ContactPhone': return <ContactPhoneIcon />;
      case 'QuestionAnswer': return <QuestionAnswerIcon />;
      case 'AppRegistration': return <AppRegistrationIcon />;
      case 'LocalFlorist': return <LocalFloristIcon />;
      default: return <InfoIcon />;
    }
  };

  const menuItems = headerContent.menuItems.map(item => ({
    text: item[language as keyof typeof item] || item.en,
    icon: getIconComponent(item.icon),
    path: item.path
  }));



  const languageItems = headerContent.languageSelector.languages.map(lang => ({
    code: lang.code,
    text: lang.text
  }));

  const drawer = (
    <Box sx={{ width: '100%' }}>
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: 3,
        borderBottom: '1px solid rgba(0, 0, 0, 0.06)'
      }}>
        <LogoFlip
          height="45px"
          width="auto"
        />
        <IconButton
          onClick={handleDrawerToggle}
          sx={{
            color: '#1B4C35',
            backgroundColor: 'rgba(27, 76, 53, 0.05)',
            '&:hover': {
              backgroundColor: 'rgba(27, 76, 53, 0.1)',
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      <List sx={{ p: 2 }}>
        {menuItems.map((item) => (
          <ListItem
            key={item.text}
            onClick={() => {
              if (item.path === '/nursery') {
                handleNurseryClick();
              } else {
                navigate(item.path);
              }
              handleDrawerToggle();
            }}
            sx={{
              cursor: 'pointer',
              mb: 1,
              borderRadius: '8px',
              transition: 'all 0.3s ease',
              '&:hover': {
                backgroundColor: 'rgba(27, 76, 53, 0.05)',
              }
            }}
          >
            <ListItemIcon sx={{ color: '#1B4C35', minWidth: '40px' }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              primaryTypographyProps={{
                sx: {
                  fontWeight: 500,
                  color: '#333'
                }
              }}
            />
          </ListItem>
        ))}
        <ListItem
          onClick={handlePoliciesClick}
          sx={{
            cursor: 'pointer',
            mb: 1,
            borderRadius: '8px',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: 'rgba(27, 76, 53, 0.05)',
            }
          }}
        >
          <ListItemIcon sx={{ color: '#1B4C35', minWidth: '40px' }}>
            <GavelIcon />
          </ListItemIcon>
          <ListItemText
            primary={t.termsAndConditions}
            primaryTypographyProps={{
              sx: {
                fontWeight: 500,
                color: '#333'
              }
            }}
          />
        </ListItem>

        <Divider sx={{ my: 2 }} />

        <ListItem sx={{ mb: 1 }}>
          <ListItemIcon sx={{ color: '#1B4C35', minWidth: '40px' }}>
            <LanguageIcon />
          </ListItemIcon>
          <ListItemText
            primary={getLanguageText()}
            primaryTypographyProps={{
              sx: {
                fontWeight: 500,
                color: '#333'
              }
            }}
          />
        </ListItem>

        <Box sx={{ pl: 2, pr: 2 }}>
          <ToggleButtonGroup
            value={language}
            exclusive
            onChange={(_, newLang) => newLang && setLanguage(newLang)}
            aria-label="language selection"
            fullWidth
            size="small"
            sx={{ mb: 1 }}
          >
            {languageItems.map((item) => (
              <ToggleButton
                key={item.code}
                value={item.code}
                sx={{
                  fontWeight: language === item.code ? 600 : 400,
                  color: language === item.code ? '#1B4C35' : 'inherit',
                  '&.Mui-selected': {
                    backgroundColor: 'rgba(27, 76, 53, 0.1)',
                  }
                }}
              >
                {item.text}
              </ToggleButton>
            ))}
          </ToggleButtonGroup>
        </Box>
      </List>
      <Box sx={{ p: 3, mt: 2 }}>
        <Button
          variant="contained"
          fullWidth
          component={RouterLink}
          to="/form"
          sx={{
            backgroundColor: '#1B4C35',
            color: 'white',
            fontWeight: 600,
            textTransform: 'none',
            py: 1.5,
            borderRadius: '8px',
            boxShadow: '0 4px 10px rgba(27, 76, 53, 0.2)',
            '&:hover': {
              backgroundColor: '#4CAF50',
              boxShadow: '0 6px 15px rgba(27, 76, 53, 0.3)',
            },
            mb: 2
          }}
        >
          {t.register}
        </Button>
        <Button
          variant="contained"
          fullWidth
          component="a"
          href="https://wa.me/919986890777"
          target="_blank"
          rel="noopener noreferrer"
          startIcon={<WhatsAppIcon />}
          sx={{
            backgroundColor: '#25D366',
            color: 'white',
            fontWeight: 600,
            textTransform: 'none',
            py: 1.5,
            borderRadius: '8px',
            boxShadow: '0 4px 10px rgba(37, 211, 102, 0.2)',
            '&:hover': {
              backgroundColor: '#128C7E',
              boxShadow: '0 6px 15px rgba(37, 211, 102, 0.3)',
            }
          }}
        >
          {language === 'en' ? 'WhatsApp Us' : language === 'kn' ? 'ವಾಟ್ಸಾಪ್ ನಲ್ಲಿ ಸಂಪರ್ಕಿಸಿ' : 'वाट्सएप पर संपर्क करें'}
        </Button>
      </Box>
    </Box>
  );

  return (
    <>
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          backgroundColor: '#fff',
          borderBottom: '1px solid rgba(0, 0, 0, 0.05)'
        }}
      >
        <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 }, margin: '0 auto' }}>
          <Toolbar sx={{
            padding: { xs: '0.5rem 0', md: '0.75rem 0' },
            minHeight: { xs: '60px', sm: '70px', md: '75px' },
            justifyContent: 'space-between',
            width: '100%',
            alignItems: 'center'
          }}>
            <Box
              component={RouterLink}
              to="/"
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                mr: { xs: 1, sm: 2, md: 3 },
                flexShrink: 0
              }}
            >
              <LogoFlip
                height={{ xs: '40px', sm: '45px', md: '50px' }}
                width="auto"
              />
            </Box>

            {/* Desktop Menu */}
            {!isMobile && (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: { sm: '0.3rem', md: '0.5rem', lg: '0.75rem' },
                flex: 1,
                ml: { sm: 2, md: 3, lg: 4 },
                mr: { sm: 2, md: 3 }
              }}>
                {menuItems.map((item) => (
                  <Button
                    key={item.text}
                    component={item.path === '/nursery' ? 'button' : RouterLink}
                    to={item.path === '/nursery' ? undefined : item.path}
                    onClick={item.path === '/nursery' ? handleNurseryClick : undefined}
                    sx={{
                      color: '#333',
                      fontWeight: 500,
                      fontSize: { sm: '0.8rem', md: '0.85rem', lg: '0.9rem' },
                      textTransform: 'none',
                      px: { sm: 0.75, md: 1, lg: 1.25 },
                      py: { sm: 0.75, md: 1 },
                      whiteSpace: 'nowrap',
                      borderRadius: '6px',
                      transition: 'all 0.2s ease',
                      minWidth: 'auto',
                      flex: '0 0 auto',
                      '&:hover': {
                        backgroundColor: 'rgba(27, 76, 53, 0.05)',
                        color: '#1B4C35',
                        transform: 'translateY(-1px)'
                      },
                      '&.active': {
                        color: '#1B4C35',
                        fontWeight: 600,
                        backgroundColor: 'rgba(27, 76, 53, 0.05)'
                      }
                    }}
                  >
                    {item.text}
                  </Button>
                ))}
                <Button
                  onClick={handlePoliciesClick}
                  sx={{
                    color: '#333',
                    fontWeight: 500,
                    fontSize: { sm: '0.8rem', md: '0.85rem', lg: '0.9rem' },
                    textTransform: 'none',
                    px: { sm: 0.75, md: 1, lg: 1.25 },
                    py: { sm: 0.75, md: 1 },
                    borderRadius: '6px',
                    transition: 'all 0.2s ease',
                    minWidth: 'auto',
                    flex: '0 0 auto',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.05)',
                      color: '#1B4C35',
                      transform: 'translateY(-1px)'
                    }
                  }}
                >
                  {t.termsAndConditions}
                </Button>

                <Button
                  onClick={handleLanguageClick}
                  startIcon={<LanguageIcon sx={{ fontSize: { sm: '18px', md: '20px' } }} />}
                  sx={{
                    color: '#333',
                    fontWeight: 500,
                    fontSize: { sm: '0.8rem', md: '0.85rem', lg: '0.9rem' },
                    textTransform: 'none',
                    px: { sm: 0.75, md: 1, lg: 1.25 },
                    py: { sm: 0.75, md: 1 },
                    borderRadius: '6px',
                    transition: 'all 0.2s ease',
                    minWidth: 'auto',
                    flex: '0 0 auto',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.05)',
                      color: '#1B4C35',
                      transform: 'translateY(-1px)'
                    }
                  }}
                >
                  {headerContent.languageSelector.languages.find(item => item.code === language)?.text}
                </Button>

                <Button
                  variant="contained"
                  component={RouterLink}
                  to="/form"
                  sx={{
                    ml: { sm: 1, md: 1.5, lg: 2 },
                    backgroundColor: '#1B4C35',
                    color: 'white',
                    fontWeight: 600,
                    textTransform: 'none',
                    px: { sm: 1.25, md: 1.5, lg: 2 },
                    py: { sm: 0.75, md: 1 },
                    borderRadius: '6px',
                    whiteSpace: 'nowrap',
                    fontSize: { sm: '0.8rem', md: '0.85rem', lg: '0.9rem' },
                    flex: '0 0 auto',
                    boxShadow: '0 2px 8px rgba(27, 76, 53, 0.2)',
                    '&:hover': {
                      backgroundColor: '#156C4A',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(27, 76, 53, 0.3)'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  {t.register}
                </Button>
                <IconButton
                  component="a"
                  href="https://wa.me/919986890777"
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    ml: { sm: 1, md: 1.5 },
                    backgroundColor: '#25D366',
                    color: 'white',
                    padding: { sm: '8px', md: '10px' },
                    minWidth: { sm: '40px', md: '44px' },
                    height: { sm: '40px', md: '44px' },
                    borderRadius: '8px',
                    boxShadow: '0 2px 8px rgba(37, 211, 102, 0.2)',
                    '&:hover': {
                      backgroundColor: '#128C7E',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(37, 211, 102, 0.3)'
                    },
                    transition: 'all 0.3s ease',
                    flex: '0 0 auto'
                  }}
                >
                  <WhatsAppIcon sx={{ fontSize: { sm: '20px', md: '22px' } }} />
                </IconButton>
              </Box>
            )}

            {/* Mobile Navigation */}
            {isMobile && (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                ml: 'auto',
                gap: { xs: 0.5, sm: 0.75 },
                flexShrink: 0
              }}>
                {/* Nursery Button */}
                <Button
                  onClick={handleNurseryClick}
                  startIcon={<LocalFloristIcon sx={{ fontSize: '16px' }} />}
                  sx={{
                    color: '#333',
                    fontWeight: 500,
                    fontSize: { xs: '0.65rem', sm: '0.7rem' },
                    textTransform: 'none',
                    padding: { xs: '4px 6px', sm: '5px 8px' },
                    borderRadius: '6px',
                    whiteSpace: 'nowrap',
                    minWidth: 'auto',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.05)',
                      color: '#1B4C35'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  {menuItems.find(item => item.path === '/nursery')?.text || 'Nursery'}
                </Button>

                {/* Language Button */}
                <Button
                  onClick={handleLanguageClick}
                  startIcon={<LanguageIcon sx={{ fontSize: '16px' }} />}
                  sx={{
                    color: '#333',
                    fontWeight: 500,
                    fontSize: { xs: '0.65rem', sm: '0.7rem' },
                    textTransform: 'none',
                    padding: { xs: '4px 6px', sm: '5px 8px' },
                    borderRadius: '6px',
                    whiteSpace: 'nowrap',
                    minWidth: 'auto',
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.05)',
                      color: '#1B4C35'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  {headerContent.languageSelector.languages.find(item => item.code === language)?.text}
                </Button>

                {/* Menu Button */}
                <IconButton
                  aria-label="open drawer"
                  edge="end"
                  onClick={handleDrawerToggle}
                  sx={{
                    color: '#1B4C35',
                    backgroundColor: 'rgba(27, 76, 53, 0.05)',
                    borderRadius: '8px',
                    padding: { xs: '8px', sm: '10px' },
                    ml: { xs: 0.5, sm: 0.75 },
                    '&:hover': {
                      backgroundColor: 'rgba(27, 76, 53, 0.1)',
                      transform: 'scale(1.05)'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  <MenuIcon sx={{ fontSize: { xs: '20px', sm: '22px' } }} />
                </IconButton>
              </Box>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      {/* Contact Note with Phone Link - Only shown on registration form page */}
      {isRegistrationPage && (
        <Alert
          severity="info"
          icon={false}
          sx={{
            borderRadius: 0,
            backgroundColor: '#FFF8E1',
            color: '#FF8F00',
            py: { xs: 1, sm: 0.75 },
            display: 'flex',
            justifyContent: 'center',
            '& .MuiAlert-message': {
              width: '100%',
              textAlign: 'center',
              fontWeight: 500,
              fontSize: { xs: '0.875rem', sm: '0.9rem' },
              lineHeight: 1.4
            }
          }}
        >
          {language === 'en' ? 'Other than Karnataka state, contact this number for query - ' :
           language === 'kn' ? 'ಕರ್ನಾಟಕ ರಾಜ್ಯದ ಹೊರತಾಗಿ, ಪ್ರಶ್ನೆಗಾಗಿ ಈ ಸಂಖ್ಯೆಯನ್ನು ಸಂಪರ್ಕಿಸಿ - ' :
           'कर्नाटक राज्य के अलावा, पूछताछ के लिए इस नंबर पर संपर्क करें - '}{' '}
          <Box
            component="a"
            href="tel:+919986890777"
            sx={{
              color: '#FF8F00',
              fontWeight: 700,
              textDecoration: 'underline',
              minHeight: { xs: '44px', sm: 'auto' },
              display: 'inline-flex',
              alignItems: 'center',
              px: { xs: 1, sm: 0 },
              py: { xs: 0.5, sm: 0 },
              borderRadius: { xs: '4px', sm: 0 },
              '&:hover': {
                color: '#F57C00',
                backgroundColor: { xs: 'rgba(255, 143, 0, 0.1)', sm: 'transparent' }
              }
            }}
          >
            +919986890777
          </Box>
        </Alert>
      )}



      {/* Language Menu */}
      <Menu
        id="language-menu"
        anchorEl={languageAnchorEl}
        open={Boolean(languageAnchorEl)}
        onClose={handleLanguageClose}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              mt: 1.5,
              borderRadius: '8px',
              minWidth: '180px',
              overflow: 'visible',
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {languageItems.map((item) => (
          <MenuItem
            key={item.code}
            onClick={() => handleLanguageChange(item.code as 'en' | 'kn' | 'hi')}
            selected={language === item.code}
            sx={{
              py: 1.5,
              px: 2.5,
              '&:hover': {
                backgroundColor: 'rgba(27, 76, 53, 0.05)',
              },
              '&.Mui-selected': {
                backgroundColor: 'rgba(27, 76, 53, 0.1)',
              }
            }}
          >
            <ListItemText
              primary={item.text}
              primaryTypographyProps={{
                sx: {
                  fontWeight: language === item.code ? 600 : 500,
                  fontSize: '0.95rem'
                }
              }}
            />
          </MenuItem>
        ))}
      </Menu>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 280,
            borderTopLeftRadius: '16px',
            borderBottomLeftRadius: '16px',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
          },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

export default Navbar;