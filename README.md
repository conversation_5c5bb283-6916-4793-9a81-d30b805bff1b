# Darvi Group - Agricultural Solutions

A modern web application for Darvi Group's agricultural services, including IoT management, plant nursery, and agricultural products.

## Features

- **IoT Management**: Smart agriculture monitoring with real-time data
- **Plant Nursery**: Browse and purchase quality plants
- **Multi-language Support**: English, Kannada, and Hindi
- **Responsive Design**: Optimized for all devices
- **CMS Integration**: Content management system for easy updates

## Technologies Used

- React 18 with TypeScript
- Material-UI (MUI) for components
- React Router for navigation
- Netlify CMS for content management
- Responsive design with mobile-first approach

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm start
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## Contact

- **Website**: https://darvigroup.in
- **Email**: <EMAIL>
- **Phone**: +91 99868 90777
- **Address**: #2 Totad building, <PERSON><PERSON><PERSON> road Hubli, Karnataka 580030, India

---

© 2024 Darvi Group. All rights reserved.
